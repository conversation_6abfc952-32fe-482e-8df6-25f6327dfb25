<template>
  <Dialog v-model="dialogVisible" title="选择用户" width="800px">
    <!-- 搜索表单 -->
    <el-form
      ref="queryFormRef"
      :model="queryParams"
      :inline="true"
      label-width="68px"
      class="mb-4"
    >
      <el-form-item label="昵称" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入用户昵称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="phoneNumber">
        <el-input
          v-model="queryParams.phoneNumber"
          placeholder="请输入手机号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery" type="primary">
          <Icon icon="ep:search" class="mr-5px" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" />
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 用户表格 -->
    <el-table
      v-loading="loading"
      :data="list"
      highlight-current-row
      @current-change="handleCurrentChange"
      style="width: 100%"
    >
      <el-table-column prop="nickname" label="用户昵称" width="150" />
      <el-table-column prop="phoneNumber" label="手机号" width="150" />
      <el-table-column prop="gender" label="性别" width="80">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_SEX" :value="scope.row.gender" />
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="80">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status ? 0 : 1" />
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180" :formatter="dateFormatter" />
      <el-table-column label="操作" width="100">
        <template #default="scope">
          <el-button type="primary" link @click="selectUser(scope.row)">
            选择
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button 
        type="primary" 
        @click="confirmSelect" 
        :disabled="!selectedUser"
      >
        确 定
      </el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { MemberApi, MemberVO } from '@/api/rental/member'
import { DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'

/** 用户选择弹窗 */
defineOptions({ name: 'UserSelectDialog' })

const emit = defineEmits(['confirm'])

const dialogVisible = ref(false) // 弹窗的是否展示
const loading = ref(true) // 列表的加载中
const list = ref<MemberVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const selectedUser = ref<MemberVO | null>(null) // 选中的用户

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  nickname: undefined,
  phoneNumber: undefined,
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await MemberApi.getMemberPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

/** 处理当前行变化 */
const handleCurrentChange = (currentRow: MemberVO | null) => {
  selectedUser.value = currentRow
}

/** 选择用户 */
const selectUser = (user: MemberVO) => {
  selectedUser.value = user
  confirmSelect()
}

/** 确认选择 */
const confirmSelect = () => {
  if (selectedUser.value) {
    emit('confirm', selectedUser.value)
    dialogVisible.value = false
  }
}

/** 打开弹窗 */
const open = async () => {
  dialogVisible.value = true
  selectedUser.value = null
  resetQuery()
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
