<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="用户" prop="userId">
        <el-select v-model="formData.userId" placeholder="请选择用户">
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="租赁模式" prop="mode">
        <el-select v-model="formData.mode" placeholder="请选择租赁模式">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.RENTAL_CUSTOM_PRICE_MODEL)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否启用" prop="enabled">
        <el-select v-model="formData.enabled" placeholder="请选择是否启用">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.RENTAL_STORE_ENABLED)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="授权过期时间" prop="expireTime">
        <el-date-picker
          v-model="formData.expireTime"
          type="datetime"
          value-format="x"
          placeholder="选择授权过期时间"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" type="textarea" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { UserCustomPriceApi, UserCustomPriceVO } from '@/api/rental/usercustomprice'
import { DICT_TYPE, getIntDictOptions,getStrDictOptions } from '@/utils/dict'
import { MemberApi,MemberVO } from '@/api/rental/member'

/** 租赁模式 表单 */
defineOptions({ name: 'UserCustomPriceForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const userList = ref<MemberVO[]>([]) // 用户列表
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  userId: undefined,
  mode: undefined,
  enabled: undefined,
  expireTime: undefined,
  remark: undefined,
})
const formRules = reactive({
  userId: [{ required: true, message: '用户不能为空', trigger: 'change' }],
  mode: [{ required: true, message: '租赁模式不能为空', trigger: 'blur' }],
  enabled: [{ required: true, message: '是否启用不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await UserCustomPriceApi.getUserCustomPrice(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as UserCustomPriceVO
    if (formType.value === 'create') {
      await UserCustomPriceApi.createUserCustomPrice(data)
      message.success(t('common.createSuccess'))
    } else {
      await UserCustomPriceApi.updateUserCustomPrice(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    userId: undefined,
    mode: undefined,
    enabled: undefined,
    expireTime: undefined,
    remark: undefined,
  }
  formRef.value?.resetFields()
}

/** 获取用户列表 */
const getUserList = async () => {
  const data = await MemberApi.getMemberList()
  userList.value = data
}

/** 初始化 **/
onMounted(() => {
  getUserList()
})
</script>