# 用户自定义价格表单 - 用户选择优化

## 修改说明

原来的用户选择使用下拉框，当用户量大时不太方便。现在改为弹窗选择，用户信息用表格展示，支持分页和搜索。

## 主要修改

### 1. UserCustomPriceForm.vue
- 将用户选择的 `el-select` 改为 `el-input`（只读），点击时打开用户选择弹窗
- 添加了 `selectedUserName` 变量来显示选中的用户名称
- 添加了 `openUserSelect()` 方法来打开用户选择弹窗
- 添加了 `handleUserSelect()` 方法来处理用户选择结果
- 修改了 `open()` 方法，在编辑时通过API获取用户信息并显示用户名称
- 移除了不再需要的 `getUserList()` 方法和 `onMounted` 初始化

### 2. 新增 UserSelectDialog.vue 组件
- 位置：`src/views/rental/usercustomprice/components/UserSelectDialog.vue`
- 功能：
  - 支持分页显示用户列表
  - 支持按昵称和手机号搜索
  - 表格显示用户信息（昵称、手机号、性别、状态、创建时间）
  - 支持点击行选择或点击"选择"按钮
  - 确认选择后返回选中的用户信息

## 使用方式

1. 在表单中点击用户输入框
2. 弹出用户选择对话框
3. 可以通过昵称或手机号搜索用户
4. 点击表格行或"选择"按钮选择用户
5. 点击"确定"按钮确认选择

## 技术特点

- 使用分页加载，避免一次性加载大量用户数据
- 支持实时搜索，提高用户查找效率
- 表格展示更多用户信息，便于识别
- 保持原有的表单验证和数据处理逻辑
- 编辑时能正确显示已选择的用户名称

## API 依赖

- `MemberApi.getMemberPage()` - 分页获取用户列表
- `MemberApi.getMember()` - 根据ID获取单个用户信息（用于编辑时显示用户名称）
